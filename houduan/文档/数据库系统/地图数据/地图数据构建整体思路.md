好的，现在我需要制作一个地图数据的方法，这个方法我会传入我需要获取的地图id，以及我需要获取的字段名字
你可以从地图数据结构.md中看到我两个表中的字段和表的名字
首先是地图id，地图id不一定是数字，他可能是其他的字母等等，然后我有两个表，一个是huizong一个是name，我可以告诉你name表是一个从表，所以如果查询单个字段，那只会考虑huizong表的字段
但是如果我传入的是quanbu_xinxi，那么就是获取全部信息，所有字段的内容，这样就可以从这两个表当中去获取了，就是全部数据下才需要两个表去获取
然后是redis是存储3天，但是注意，获取单个字段是不会记录redis的，只有获取全部的数据才会记录redis
redis和日志以及数据结构还有sql命令我都创建了类进行统一管理，具体你可以看一下这个包的内容，就是我不希望我的ditushujuchuli.rs里面有硬编码！
包括redis的缓存时间sql日志等等，然后是redis，我希望你能够创建一个方法，这个方法可以单独删除掉你上面所获取的数据的缓存而不会影响到其他的缓存，就是只删除这个地图的全部信息，但是不会删除怪物的，或者地图其他的缓存