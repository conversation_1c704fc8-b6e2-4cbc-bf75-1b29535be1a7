# 地图数据处理使用指南

## 概述

地图数据处理系统提供了统一的接口来获取和管理地图数据，支持单个字段查询和全部信息查询，并提供Redis缓存功能。

## 核心特性

- **单个字段查询**：只从`ditu_huizong`表查询，不使用缓存
- **全部信息查询**：合并`ditu_huizong`和`ditu_name`两个表的数据，使用Redis缓存3天
- **统一接口**：通过字段名`quanbu_xinxi`来区分查询类型
- **缓存管理**：提供单独清除指定地图缓存的功能
- **安全验证**：字段名验证，防止SQL注入

## 模块结构

```
ditushuju/
├── ditushujuchuli.rs          # 主处理类
├── ditu_sql_kongzhi.rs        # SQL控制类
├── ditu_redis_kongzhi.rs      # Redis控制类
├── ditu_rizhi_kongzhi.rs      # 日志控制类
├── ditushujujiegouti.rs       # 数据结构定义
├── ditu_shiyong_shili.rs      # 使用示例
└── mod.rs                     # 模块导出
```

## 基本使用

### 1. 创建处理实例

```rust
use crate::chushihua::shujukuxitong::youxishujuchuli::ditushuju::ditushujuchuli;

// 只使用MySQL（不缓存）
let ditu_chuliqii = ditushujuchuli::new(mysql_lianjie);

// 使用MySQL + Redis（支持缓存）
let ditu_chuliqii = ditushujuchuli::new_with_redis(mysql_lianjie, redis_lianjie);
```

### 2. 获取单个字段

```rust
// 获取地图名称
let jieguo = ditu_chuliqii.huoqu_ditu_ziduan("map_001", "ditu_mingcheng").await?;
if let Some(mingcheng) = jieguo.ziduan_zhi {
    println!("地图名称: {}", mingcheng);
}
```

### 3. 获取全部信息

```rust
// 获取全部信息（会缓存到Redis）
let jieguo = ditu_chuliqii.huoqu_ditu_quanbu_xinxi("map_001").await?;
if let Some(xinxi) = jieguo {
    println!("数据来源: {}", xinxi.shuju_laiyuan); // "缓存" 或 "数据库"
    println!("字段数量: {}", xinxi.ziduan_shuliang());
}
```

### 4. 使用统一接口

```rust
// 获取单个字段
let jieguo = ditu_chuliqii.huoqu_ditu_shuju("map_001", "ditu_mingcheng").await?;

// 获取全部信息
let jieguo = ditu_chuliqii.huoqu_ditu_shuju("map_001", "quanbu_xinxi").await?;
```

## 缓存管理

### 清除指定地图缓存

```rust
let jieguo = ditu_chuliqii.qingchu_ditu_huancun("map_001").await?;
println!("清除结果: {}", jieguo.xiaoxi);
```

### 检查缓存状态

```rust
let cunzai = ditu_chuliqii.jiancha_ditu_huancun_cunzai("map_001").await?;
println!("缓存是否存在: {}", cunzai);
```

### 获取缓存统计

```rust
let tongji = ditu_chuliqii.huoqu_ditu_huancun_tongji().await?;
println!("缓存统计: {}", tongji);
```

## 支持的字段

### ditu_huizong表字段

- `ditu_id` - 地图ID
- `ditu_mingcheng` - 地图名称
- `xianshi_mingcheng` - 显示名称
- `leixing_*` - 各种类型字段
- `fenlei_*` - 各种分类字段
- `you_*` - 是否有某种内容
- `*_zongshu` - 各种数量字段
- `*_yaml` - YAML格式数据
- 等等...

### ditu_name表字段（在全部信息中以name_table_前缀返回）

- `name_table_id` - ID
- `name_table_name` - 名称

## 特殊字段名

- `quanbu_xinxi` - 获取全部信息的特殊标识

## 缓存策略

- **单个字段查询**：不使用缓存，直接查询数据库
- **全部信息查询**：
  - 先检查Redis缓存
  - 如果缓存不存在，从数据库查询并缓存
  - 缓存时间：3天（259200秒）
  - Redis键格式：`ditu_quanbu:{ditu_id}`

## 错误处理

所有方法都返回`anyhow::Result`，包含详细的错误信息：

- 字段名验证失败
- 数据库连接失败
- 查询执行失败
- Redis操作失败
- 数据不存在

## 日志记录

系统会自动记录以下日志：

- 成功操作的信息日志
- 失败操作的错误日志
- 缓存操作的状态日志
- 数据库查询的调试日志

## 安全特性

- 字段名安全验证，防止SQL注入
- 只允许预定义的字段名
- 参数化查询，避免SQL注入风险

## 性能优化

- Redis缓存减少数据库查询
- 单个字段查询避免不必要的缓存开销
- 连接池复用，提高并发性能
- 批量操作支持

## 注意事项

1. 地图ID支持字符串类型，不限于数字
2. 只有获取全部信息时才会使用Redis缓存
3. 清除缓存只影响指定地图，不会影响其他数据
4. 字段名必须在预定义列表中，否则会验证失败
5. Redis连接是可选的，没有Redis也可以正常工作（只是没有缓存功能）

## 完整示例

参考 `ditu_shiyong_shili.rs` 文件中的完整使用示例。
