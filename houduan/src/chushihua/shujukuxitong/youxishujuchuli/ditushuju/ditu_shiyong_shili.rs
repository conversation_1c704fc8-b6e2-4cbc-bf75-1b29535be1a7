#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use super::ditushujuchuli::ditushujuchuli;

/// 地图数据处理使用示例
pub struct ditu_shiyong_shili;

impl ditu_shiyong_shili {
    /// 示例：获取地图单个字段
    pub async fn shili_huoqu_ditu_ziduan(
        mysql_lianjie: mysql_lianjie_guanli,
        ditu_id: &str,
        ziduan_ming: &str,
    ) -> anyhow::Result<()> {
        // 创建地图数据处理实例
        let ditu_chuliqii = ditushujuchuli::new(mysql_lianjie);
        
        // 获取单个字段数据
        match ditu_chuliqii.huoqu_ditu_ziduan(ditu_id, ziduan_ming).await {
            Ok(jieguo) => {
                println!("地图ID: {}", jieguo.ditu_id);
                println!("字段名: {}", jieguo.ziduan_ming);
                if let Some(zhi) = jieguo.ziduan_zhi {
                    println!("字段值: {}", zhi);
                } else {
                    println!("字段值: 无数据");
                }
            }
            Err(e) => {
                println!("获取地图字段失败: {}", e);
            }
        }
        
        Ok(())
    }

    /// 示例：获取地图全部信息（使用Redis缓存）
    pub async fn shili_huoqu_ditu_quanbu_xinxi(
        mysql_lianjie: mysql_lianjie_guanli,
        redis_lianjie: redis_lianjie_guanli,
        ditu_id: &str,
    ) -> anyhow::Result<()> {
        // 创建带Redis的地图数据处理实例
        let ditu_chuliqii = ditushujuchuli::new_with_redis(mysql_lianjie, redis_lianjie);
        
        // 获取全部信息
        match ditu_chuliqii.huoqu_ditu_quanbu_xinxi(ditu_id).await {
            Ok(Some(jieguo)) => {
                println!("地图ID: {}", jieguo.ditu_id);
                println!("数据来源: {}", jieguo.shuju_laiyuan);
                println!("字段数量: {}", jieguo.ziduan_shuliang());
                
                // 显示部分重要字段
                if let Some(mingcheng) = jieguo.huoqu_ziduan_zhi("ditu_mingcheng") {
                    println!("地图名称: {}", mingcheng);
                }
                if let Some(xianshi_mingcheng) = jieguo.huoqu_ziduan_zhi("xianshi_mingcheng") {
                    println!("显示名称: {}", xianshi_mingcheng);
                }
                
                // 显示name表的数据
                if let Some(name) = jieguo.huoqu_ziduan_zhi("name_table_name") {
                    println!("name表名称: {}", name);
                }
            }
            Ok(None) => {
                println!("地图数据不存在: {}", ditu_id);
            }
            Err(e) => {
                println!("获取地图全部信息失败: {}", e);
            }
        }
        
        Ok(())
    }

    /// 示例：使用统一接口获取数据
    pub async fn shili_tongyi_jiekou(
        mysql_lianjie: mysql_lianjie_guanli,
        redis_lianjie: redis_lianjie_guanli,
        ditu_id: &str,
        ziduan_ming: &str,
    ) -> anyhow::Result<()> {
        // 创建带Redis的地图数据处理实例
        let ditu_chuliqii = ditushujuchuli::new_with_redis(mysql_lianjie, redis_lianjie);
        
        // 使用统一接口获取数据
        match ditu_chuliqii.huoqu_ditu_shuju(ditu_id, ziduan_ming).await {
            Ok(jieguo) => {
                println!("查询结果: {}", serde_json::to_string_pretty(&jieguo)?);
            }
            Err(e) => {
                println!("查询失败: {}", e);
            }
        }
        
        Ok(())
    }

    /// 示例：缓存管理操作
    pub async fn shili_huancun_guanli(
        mysql_lianjie: mysql_lianjie_guanli,
        redis_lianjie: redis_lianjie_guanli,
        ditu_id: &str,
    ) -> anyhow::Result<()> {
        // 创建带Redis的地图数据处理实例
        let ditu_chuliqii = ditushujuchuli::new_with_redis(mysql_lianjie, redis_lianjie);
        
        // 检查缓存是否存在
        let cunzai = ditu_chuliqii.jiancha_ditu_huancun_cunzai(ditu_id).await?;
        println!("地图缓存是否存在: {}", cunzai);
        
        // 获取缓存统计信息
        let tongji = ditu_chuliqii.huoqu_ditu_huancun_tongji().await?;
        println!("缓存统计: {}", tongji);
        
        // 清除指定地图的缓存
        let qingchu_jieguo = ditu_chuliqii.qingchu_ditu_huancun(ditu_id).await?;
        println!("清除缓存结果: {}", serde_json::to_string_pretty(&qingchu_jieguo)?);
        
        Ok(())
    }

    /// 示例：完整的使用流程
    pub async fn shili_wanzheng_liucheng(
        mysql_lianjie: mysql_lianjie_guanli,
        redis_lianjie: redis_lianjie_guanli,
    ) -> anyhow::Result<()> {
        let ditu_id = "test_map_001";
        
        println!("=== 地图数据处理完整示例 ===");
        
        // 1. 获取单个字段
        println!("\n1. 获取地图名称字段:");
        Self::shili_huoqu_ditu_ziduan(
            mysql_lianjie.clone(),
            ditu_id,
            "ditu_mingcheng"
        ).await?;
        
        // 2. 获取全部信息（第一次从数据库，会缓存）
        println!("\n2. 获取全部信息（第一次）:");
        Self::shili_huoqu_ditu_quanbu_xinxi(
            mysql_lianjie.clone(),
            redis_lianjie.clone(),
            ditu_id
        ).await?;
        
        // 3. 再次获取全部信息（从缓存）
        println!("\n3. 获取全部信息（第二次，应该来自缓存）:");
        Self::shili_huoqu_ditu_quanbu_xinxi(
            mysql_lianjie.clone(),
            redis_lianjie.clone(),
            ditu_id
        ).await?;
        
        // 4. 使用统一接口
        println!("\n4. 使用统一接口获取单个字段:");
        Self::shili_tongyi_jiekou(
            mysql_lianjie.clone(),
            redis_lianjie.clone(),
            ditu_id,
            "xianshi_mingcheng"
        ).await?;
        
        println!("\n5. 使用统一接口获取全部信息:");
        Self::shili_tongyi_jiekou(
            mysql_lianjie.clone(),
            redis_lianjie.clone(),
            ditu_id,
            "quanbu_xinxi"
        ).await?;
        
        // 6. 缓存管理
        println!("\n6. 缓存管理操作:");
        Self::shili_huancun_guanli(
            mysql_lianjie,
            redis_lianjie,
            ditu_id
        ).await?;
        
        println!("\n=== 示例完成 ===");
        Ok(())
    }
}
