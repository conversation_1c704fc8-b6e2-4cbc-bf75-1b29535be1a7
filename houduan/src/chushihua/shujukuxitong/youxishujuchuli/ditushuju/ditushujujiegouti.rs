#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 地图单个字段查询结果
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ditu_ziduan_jieguo {
    /// 地图ID
    pub ditu_id: String,
    /// 字段名
    pub ziduan_ming: String,
    /// 字段值
    pub ziduan_zhi: Option<String>,
}

/// 地图全部信息查询结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_quanbu_xinxi_jieguo {
    /// 地图ID
    pub ditu_id: String,
    /// 所有字段数据（包含huizong表和name表的合并数据）
    pub suoyou_ziduan: HashMap<String, String>,
    /// 数据来源（"缓存" 或 "数据库"）
    pub shuju_laiyuan: String,
}

/// 地图缓存操作结果
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ditu_huancun_caozuo_jieguo {
    /// 地图ID
    pub ditu_id: String,
    /// 操作是否成功
    pub chenggong: bool,
    /// 操作消息
    pub xiaoxi: String,
}

/// 地图数据查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_chaxun_canshu {
    /// 地图ID
    pub ditu_id: String,
    /// 字段名（如果是"quanbu_xinxi"则获取全部信息）
    pub ziduan_ming: String,
}

impl ditu_ziduan_jieguo {
    /// 创建新的地图字段查询结果
    pub fn new(ditu_id: String, ziduan_ming: String, ziduan_zhi: Option<String>) -> Self {
        Self {
            ditu_id,
            ziduan_ming,
            ziduan_zhi,
        }
    }

    /// 检查字段值是否存在
    pub fn you_zhi(&self) -> bool {
        self.ziduan_zhi.is_some()
    }
}

impl ditu_quanbu_xinxi_jieguo {
    /// 创建新的地图全部信息查询结果
    pub fn new(ditu_id: String, suoyou_ziduan: HashMap<String, String>, shuju_laiyuan: String) -> Self {
        Self {
            ditu_id,
            suoyou_ziduan,
            shuju_laiyuan,
        }
    }

    /// 获取指定字段的值
    pub fn huoqu_ziduan_zhi(&self, ziduan_ming: &str) -> Option<&String> {
        self.suoyou_ziduan.get(ziduan_ming)
    }

    /// 获取字段数量
    pub fn ziduan_shuliang(&self) -> usize {
        self.suoyou_ziduan.len()
    }

    /// 检查是否来自缓存
    pub fn laizi_huancun(&self) -> bool {
        self.shuju_laiyuan == "缓存"
    }
}

impl ditu_huancun_caozuo_jieguo {
    /// 创建成功的缓存操作结果
    pub fn chenggong(ditu_id: String, xiaoxi: String) -> Self {
        Self {
            ditu_id,
            chenggong: true,
            xiaoxi,
        }
    }

    /// 创建失败的缓存操作结果
    pub fn shibai(ditu_id: String, xiaoxi: String) -> Self {
        Self {
            ditu_id,
            chenggong: false,
            xiaoxi,
        }
    }
}

impl ditu_chaxun_canshu {
    /// 创建新的地图查询参数
    pub fn new(ditu_id: String, ziduan_ming: String) -> Self {
        Self {
            ditu_id,
            ziduan_ming,
        }
    }

    /// 检查是否是获取全部信息的查询
    pub fn shi_quanbu_xinxi_chaxun(&self) -> bool {
        self.ziduan_ming == "quanbu_xinxi"
    }
}